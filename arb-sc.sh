#depots
python ARB_run_self_consistency.py --domain 'depots' --task 'action_executability' --file_path './ARB-Benchmark/depots/depots-action_executability-true_false_answer.jsonl' --is0ShotCoT --save_name '4o-mini-arb-sc-depots-ae' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'depots' --task 'effects' --file_path './ARB-Benchmark/depots/depots-effects-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-depots-eff' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'depots' --task 'fluent_tracking' --file_path './ARB-Benchmark/depots/depots-fluent_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-depots-ft' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'depots' --task 'state_tracking' --file_path './ARB-Benchmark/depots/depots-state_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-depots-st' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5




#driverlog
python ARB_run_self_consistency.py --domain 'driverlog' --task 'action_executability' --file_path './ARB-Benchmark/driverlog/driverlog-action_executability-true_false_answer.jsonl' --is0ShotCoT --save_name '4o-mini-arb-sc-driverlog-ae' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'driverlog' --task 'effects' --file_path './ARB-Benchmark/driverlog/driverlog-effects-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-driverlog-eff' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'driverlog' --task 'fluent_tracking' --file_path './ARB-Benchmark/driverlog/driverlog-fluent_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-driverlog-ft' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'driverlog' --task 'state_tracking' --file_path './ARB-Benchmark/driverlog/driverlog-state_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-driverlog-st' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5


#mystery
python ARB_run_self_consistency.py --domain 'mystery' --task 'action_executability' --file_path './ARB-Benchmark/mystery/mystery-action_executability-true_false_answer.jsonl' --is0ShotCoT --save_name '4o-mini-arb-sc-mystery-ae' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'mystery' --task 'effects' --file_path './ARB-Benchmark/mystery/mystery-effects-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-mystery-eff' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'mystery' --task 'fluent_tracking' --file_path './ARB-Benchmark/mystery/mystery-fluent_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-mystery-ft' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'mystery' --task 'state_tracking' --file_path './ARB-Benchmark/mystery/mystery-state_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-mystery-st' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5



#grippers
python ARB_run_self_consistency.py --domain 'grippers' --task 'action_executability' --file_path './ARB-Benchmark/grippers/grippers-action_executability-true_false_answer.jsonl' --is0ShotCoT --save_name '4o-mini-arb-sc-grippers-ae' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'grippers' --task 'effects' --file_path './ARB-Benchmark/grippers/grippers-effects-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-grippers-eff' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'grippers' --task 'fluent_tracking' --file_path './ARB-Benchmark/grippers/grippers-fluent_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-grippers-ft' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'grippers' --task 'state_tracking' --file_path './ARB-Benchmark/grippers/grippers-state_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-grippers-st' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5




#satellite
python ARB_run_self_consistency.py --domain 'satellite' --task 'action_executability' --file_path './ARB-Benchmark/satellite/satellite-action_executability-true_false_answer.jsonl' --is0ShotCoT --save_name 'arb-sc-satellite-ae' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'satellite' --task 'effects' --file_path './ARB-Benchmark/satellite/satellite-effects-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-satellite-eff' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'satellite' --task 'fluent_tracking' --file_path './ARB-Benchmark/satellite/satellite-fluent_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-satellite-ft' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'satellite' --task 'state_tracking' --file_path './ARB-Benchmark/satellite/satellite-state_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-satellite-st' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5


#spanner
python ARB_run_self_consistency.py --domain 'spanner' --task 'action_executability' --file_path './ARB-Benchmark/spanner/spanner-action_executability-true_false_answer.jsonl' --is0ShotCoT --save_name '4o-mini-arb-sc-spanner-ae' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'spanner' --task 'effects' --file_path './ARB-Benchmark/spanner/spanner-effects-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-spanner-eff' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'spanner' --task 'fluent_tracking' --file_path './ARB-Benchmark/spanner/spanner-fluent_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-spanner-ft' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'spanner' --task 'state_tracking' --file_path './ARB-Benchmark/spanner/spanner-state_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-spanner-st' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5



#visitall
python ARB_run_self_consistency.py --domain 'visitall' --task 'action_executability' --file_path './ARB-Benchmark/visitall/visitall-action_executability-true_false_answer.jsonl' --is0ShotCoT --save_name '4o-mini-arb-sc-visitall-ae' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'visitall' --task 'effects' --file_path './ARB-Benchmark/visitall/visitall-effects-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-visitall-eff' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'visitall' --task 'fluent_tracking' --file_path './ARB-Benchmark/visitall/visitall-fluent_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-visitall-ft' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5

python ARB_run_self_consistency.py --domain 'visitall' --task 'state_tracking' --file_path './ARB-Benchmark/visitall/visitall-state_tracking-true_false_answer.jsonl' --is0Shot --save_name '4o-mini-arb-sc-visitall-st' --model "gpt-4o-mini" --url 'https://api.v3.cm/v1' --api 'sk-TM5THVuRVICLiltXC2Db8bDbBeB34694B6Ad56411e8a3f39' --num_samples 5